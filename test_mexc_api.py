#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient

async def test_mexc_connection():
    print("🔄 Testing MEXC API connection...")
    
    async with MEXCClient() as client:
        print("\n📊 Testing Price Data...")
        price_data = await client.get_price_data()
        if price_data:
            print("✅ Price data retrieved successfully:")
            print(f"   Current Price: ${price_data['current_price']:.2f}")
            print(f"   Day Open: ${price_data['day_open_price']:.2f}")
            print(f"   24h Change: ${price_data['price_change_24h']:.2f}")
        else:
            print("❌ Failed to get price data")
        
        print("\n💰 Testing Account Info...")
        account_info = await client.get_account_info()
        if account_info:
            print("✅ Account info retrieved successfully:")
            total_balance = client.calculate_total_balance(account_info)
            available_balance = client.calculate_available_balance(account_info)
            print(f"   Total Balance: ${total_balance:.2f}")
            print(f"   Available Balance: ${available_balance:.2f}")
            
            print("\n📋 Account Balances:")
            for balance in account_info.get('balances', []):
                free = float(balance.get('free', 0))
                locked = float(balance.get('locked', 0))
                if free > 0 or locked > 0:
                    print(f"   {balance['asset']}: Free={free:.4f}, Locked={locked:.4f}")
        else:
            print("❌ Failed to get account info")
        
        print("\n📋 Testing Open Orders...")
        open_orders = await client.get_open_orders()
        if open_orders is not None:
            print(f"✅ Open orders retrieved: {len(open_orders)} orders")
            for order in open_orders[:5]:  # Show first 5 orders
                print(f"   {order.get('symbol')} {order.get('side')} {order.get('type')}")
        else:
            print("❌ Failed to get open orders")

if __name__ == "__main__":
    asyncio.run(test_mexc_connection())
