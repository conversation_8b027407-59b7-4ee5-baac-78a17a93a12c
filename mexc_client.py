import aiohttp
import asyncio
import json
import logging
import hmac
import hashlib
import time
from typing import Dict, Optional, List
import yaml

class MEXCClient:
    def __init__(self):
        self.base_url = "https://api.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_trading_config()

    def load_trading_config(self):
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
        except Exception as e:
            logging.warning(f"Could not load MEXC trading config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    def _generate_signature(self, query_string: str) -> str:
        if not self.api_secret:
            return ""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _get_headers(self, signed: bool = False) -> Dict:
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MEXC-Bot/1.0'
        }
        if signed and self.api_key:
            headers['X-MEXC-APIKEY'] = self.api_key
        return headers
    
    async def get_ticker_24hr(self, symbol: str = "PAXGUSDT") -> Optional[Dict]:
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting ticker: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_ticker_24hr: {e}")
            return None
    
    async def get_klines(self, symbol: str = "PAXGUSDT", interval: str = "1d", limit: int = 1) -> Optional[list]:
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                "symbol": symbol,
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logging.error(f"Error getting klines: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_klines: {e}")
            return None
    
    async def get_price_data(self) -> Optional[Dict]:
        ticker_data = await self.get_ticker_24hr()
        kline_data = await self.get_klines(interval="1d", limit=1)

        if not ticker_data:
            return None

        def safe_float(value, default=0.0):
            try:
                return float(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        def safe_int(value, default=0):
            try:
                return int(value) if value is not None else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))

        # Lấy giá mở cửa từ nến ngày hiện tại
        day_open_price = current_price  # fallback
        if kline_data and len(kline_data) > 0:
            # Kline format: [open_time, open, high, low, close, volume, close_time, quote_volume, count, taker_buy_volume, taker_buy_quote_volume, ignore]
            day_open_price = safe_float(kline_data[0][1])  # index 1 là open price

        # Tính toán thay đổi so với giá mở cửa ngày
        day_price_change = current_price - day_open_price
        day_price_change_percent = (day_price_change / day_open_price * 100) if day_open_price > 0 else 0.0

        # Thông tin 24h từ ticker
        price_change_24h = safe_float(ticker_data.get("priceChange"))
        open_price_24h = safe_float(ticker_data.get("openPrice"))
        price_change_percent_24h = (price_change_24h / open_price_24h * 100) if open_price_24h > 0 else 0.0

        result = {
            "symbol": ticker_data.get("symbol", "PAXGUSDT"),
            "current_price": current_price,
            "day_open_price": day_open_price,  # Giá mở cửa nến ngày
            "day_price_change": day_price_change,  # Thay đổi so với mở cửa ngày
            "day_price_change_percent": day_price_change_percent,  # % thay đổi so với mở cửa ngày
            "open_price_24h": open_price_24h,  # Giá mở cửa 24h
            "price_change_24h": price_change_24h,  # Thay đổi 24h
            "price_change_percent_24h": price_change_percent_24h,  # % thay đổi 24h
            "high_price": safe_float(ticker_data.get("highPrice")),
            "low_price": safe_float(ticker_data.get("lowPrice")),
            "volume": safe_float(ticker_data.get("volume")),
            "quote_volume": safe_float(ticker_data.get("quoteVolume"))
        }

        return result

    async def get_account_info(self) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            logging.warning("API credentials not configured for account info")
            return None

        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)

            url = f"{self.base_url}/api/v3/account"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }

            headers = self._get_headers(signed=True)

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Error getting account info: {response.status}")
                    text = await response.text()
                    logging.error(f"Response: {text}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_account_info: {e}")
            return None

    async def get_futures_account(self) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            logging.warning("API credentials not configured for futures account")
            return None

        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)

            url = f"{self.base_url}/api/v3/account"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }

            headers = self._get_headers(signed=True)

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Error getting futures account: {response.status}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_futures_account: {e}")
            return None

    async def get_open_orders(self) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)

            url = f"{self.base_url}/api/v3/openOrders"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }

            headers = self._get_headers(signed=True)

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Error getting open orders: {response.status}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_open_orders: {e}")
            return []

    def calculate_total_balance(self, account_data: Dict) -> float:
        if not account_data or 'balances' not in account_data:
            return 0.0

        total_usdt = 0.0
        for balance in account_data['balances']:
            if balance['asset'] == 'USDT':
                free = float(balance.get('free', 0))
                locked = float(balance.get('locked', 0))
                total_usdt += free + locked

        return total_usdt

    def calculate_available_balance(self, account_data: Dict) -> float:
        if not account_data or 'balances' not in account_data:
            return 0.0

        for balance in account_data['balances']:
            if balance['asset'] == 'USDT':
                return float(balance.get('free', 0))

        return 0.0

    async def get_futures_positions(self) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            return []

        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)

            # Try futures API endpoint
            url = f"https://contract.mexc.com/api/v1/private/position/list/all"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }

            headers = self._get_headers(signed=True)

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success') and data.get('data'):
                        positions = data['data']
                        # Filter only positions with non-zero size
                        active_positions = []
                        for pos in positions:
                            if float(pos.get('positionSize', 0)) != 0:
                                active_positions.append(pos)
                        return active_positions
                    return []
                else:
                    logging.error(f"Error getting futures positions: {response.status}")
                    text = await response.text()
                    logging.error(f"Response: {text}")
                    return []
        except Exception as e:
            logging.error(f"Exception in get_futures_positions: {e}")
            return []

    async def get_futures_balance(self) -> Optional[Dict]:
        if not self.api_key or not self.api_secret:
            return None

        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)

            # Try futures API endpoint
            url = f"https://contract.mexc.com/api/v1/private/account/assets"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }

            headers = self._get_headers(signed=True)

            async with self.session.get(url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        return data.get('data', {})
                    return None
                else:
                    logging.error(f"Error getting futures balance: {response.status}")
                    text = await response.text()
                    logging.error(f"Response: {text}")
                    return None
        except Exception as e:
            logging.error(f"Exception in get_futures_balance: {e}")
            return None
