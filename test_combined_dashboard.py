#!/usr/bin/env python3
import asyncio
from mexc_client import MEXCClient
from discord_bot import PAXGBot

async def test_combined_dashboard():
    print("🔄 Testing Combined Dashboard...")
    print("=" * 60)
    
    # Test 1: MEXC Watchlist Data
    print("\n1️⃣ Testing MEXC Watchlist Data...")
    async with MEXCClient() as client:
        watchlist_data = await client.get_watchlist_data()
        if watchlist_data:
            print("✅ Watchlist data retrieved successfully")
            watchlist = watchlist_data.get('watchlist', [])
            print(f"   Found {len(watchlist)} tokens")
            
            # Show sample data
            for item in watchlist[:5]:
                token = item['token']
                price = item['price']
                change = item['change_percent_24h']
                volume = item['volume_str']
                icon = "🟢" if change >= 0 else "🔴"
                print(f"   {token}: ${price:.2f} {icon} {abs(change):.2f}% Vol: {volume}")
        else:
            print("❌ Failed to get watchlist data")
            return
    
    # Test 2: Account Info
    print("\n2️⃣ Testing Account Info...")
    async with MEXCClient() as client:
        account_info = await client.get_account_info()
        if account_info:
            print("✅ Account info retrieved successfully")
            total_balance = client.calculate_total_balance(account_info)
            available_balance = client.calculate_available_balance(account_info)
            print(f"   Total Balance: ${total_balance:.2f}")
            print(f"   Available Balance: ${available_balance:.2f}")
        else:
            print("⚠️  Account info not available (API credentials may be missing)")
    
    # Test 3: Futures Positions
    print("\n3️⃣ Testing Futures Positions...")
    async with MEXCClient() as client:
        futures_positions = await client.get_futures_positions()
        if futures_positions is not None:
            print(f"✅ Futures positions retrieved: {len(futures_positions)} active positions")
            for pos in futures_positions[:3]:
                symbol = pos.get('symbol', 'N/A')
                size = pos.get('positionSize', 0)
                pnl = pos.get('unrealizedPnl', 0)
                print(f"   {symbol}: Size={size}, P&L=${pnl}")
        else:
            print("⚠️  Futures positions not available")
    
    # Test 4: Open Orders
    print("\n4️⃣ Testing Open Orders...")
    async with MEXCClient() as client:
        open_orders = await client.get_open_orders()
        if open_orders is not None:
            print(f"✅ Open orders retrieved: {len(open_orders)} orders")
        else:
            print("⚠️  Open orders not available")
    
    # Test 5: Combined Dashboard Creation
    print("\n5️⃣ Testing Combined Dashboard Creation...")
    bot = PAXGBot()
    combined_embed = await bot.create_combined_dashboard()
    
    if combined_embed:
        print("✅ Combined dashboard created successfully")
        print(f"   Title: {combined_embed.title}")
        print(f"   Color: {combined_embed.color}")
        print(f"   Description length: {len(combined_embed.description)} chars")
        
        # Show preview
        print("\n📋 Dashboard Preview:")
        print("=" * 50)
        content = combined_embed.description
        lines = content.split('\n')
        for line in lines[:25]:  # Show first 25 lines
            print(line)
        if len(lines) > 25:
            print("... (truncated)")
        print("=" * 50)
        
        print(f"\n📊 Footer: {combined_embed.footer.text}")
    else:
        print("❌ Failed to create combined dashboard")
    
    print("\n✅ Combined Dashboard Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_combined_dashboard())
