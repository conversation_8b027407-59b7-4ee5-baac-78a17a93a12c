import discord
from discord.ext import commands, tasks
import asyncio
import yaml
import logging
from datetime import datetime
from typing import Optional
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO)

class PAXGBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = False
        super().__init__(command_prefix='!', intents=intents)
        
        self.config = self.load_config()
        self.paxg_channel = None
        self.pinned_message = None
        self.last_price = 0
        self.last_volume = 0
        self.price_threshold = 0.5  # 0.5% thay đổi giá
        self.volume_threshold = 20  # 20% thay đổi volume
        
    def load_config(self):
        with open('config.ymal', 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    
    async def on_ready(self):
        print(f'{self.user} đã kết nối thành công!')
        
        guild = self.get_guild(int(self.config['discord']['guild_id']))
        if guild:
            self.paxg_channel = discord.utils.get(guild.channels, name='paxg')
            if not self.paxg_channel:
                print("Không tìm thấy channel 'paxg'. Vui lòng tạo channel này.")
                return
        
        self.price_monitor.start()
        await self.send_initial_dashboard()
    
    async def send_initial_dashboard(self):
        async with MEXCClient() as client:
            combined_embed = await self.create_combined_dashboard()
            if combined_embed:
                message = await self.paxg_channel.send(embed=combined_embed)
                await message.pin()
                self.pinned_message = message

                # Update last price for monitoring
                price_data = await client.get_price_data()
                if price_data:
                    self.last_price = price_data['current_price']
                    self.last_volume = price_data['volume']
    
    def create_price_embed(self, data, is_initial=False, alert_type=None):
        current_price = data['current_price']
        day_open_price = data['day_open_price']
        day_price_change = data['day_price_change']
        day_price_change_percent = data['day_price_change_percent']
        price_change_24h = data['price_change_24h']
        price_change_percent_24h = data['price_change_percent_24h']

        color = discord.Color.green() if day_price_change >= 0 else discord.Color.red()

        if alert_type:
            if alert_type == "price":
                title = "🚨 CẢNH BÁO GIÁ PAXG"
            elif alert_type == "volume":
                title = "🚨 CẢNH BÁO VOLUME PAXG"
            else:
                title = "🚨 CẢNH BÁO PAXG"
        elif is_initial:
            title = "📊 CẬP NHẬT GIÁ PAXG"
        else:
            title = "📊 CẬP NHẬT GIÁ PAXG"

        embed = discord.Embed(title=title, color=color, timestamp=datetime.now())

        # Format giống watchlist - các cột thẳng hàng với monospace
        day_change_icon = "🔴" if day_price_change < 0 else "🟢"
        change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

        # Tạo nội dung theo format watchlist với cột thẳng hàng sử dụng monospace
        content = f"""```
🧈 Giá hiện tại      ${current_price:>8,.2f}
{day_change_icon} CHG DAY           ${abs(day_price_change):>8,.2f}
📊 Giá mở cửa ngày   ${day_open_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${data['low_price']:>8,.2f}
💎 Volume 24h      {data['volume']:>8,.2f} PAXG
```"""

        embed.description = content
        embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")

        return embed

    async def create_combined_dashboard(self) -> Optional[discord.Embed]:
        async with MEXCClient() as client:
            # Get all required data
            watchlist_data = await client.get_watchlist_data()
            account_info = await client.get_account_info()
            open_orders = await client.get_open_orders()
            futures_positions = await client.get_futures_positions()

            if not watchlist_data:
                logging.error("Failed to get watchlist data")
                return None

            embed = discord.Embed(
                title="🔥 CRYPTO WATCHLIST ✅",
                color=discord.Color.dark_green(),
                timestamp=datetime.now()
            )

            # Market Indicators Section
            indicators_content = "📊 **MARKET INDICATORS**\n"

            # Find specific tokens for indicators
            watchlist = watchlist_data.get('watchlist', [])
            btc_data = next((item for item in watchlist if item['token'] == 'BTC'), None)
            paxg_data = next((item for item in watchlist if item['token'] == 'PAXG'), None)
            xau_data = next((item for item in watchlist if item['token'] == 'XAU'), None)
            usdt_data = next((item for item in watchlist if item['token'] == 'USDT'), None)

            if btc_data:
                btc_icon = "🟢" if btc_data['change_percent_24h'] >= 0 else "🔴"
                indicators_content += f"₿ **BTCDOM:** {btc_data['price']:.0f} {btc_icon} {abs(btc_data['change_percent_24h']):.1f}%\n"

            if paxg_data:
                paxg_icon = "🟢" if paxg_data['change_percent_24h'] >= 0 else "🔴"
                indicators_content += f"🥇 **PAXG:** ${paxg_data['price']:.0f} {paxg_icon} {abs(paxg_data['change_percent_24h']):.1f}%\n"

            if xau_data:
                xau_icon = "🟢" if xau_data['change_percent_24h'] >= 0 else "🔴"
                indicators_content += f"🥇 **XAU:** ${xau_data['price']:.0f} {xau_icon} {abs(xau_data['change_percent_24h']):.1f}%\n"

            if usdt_data:
                usdt_icon = "🟢" if usdt_data['change_percent_24h'] >= 0 else "🔴"
                indicators_content += f"💎 **USDT:** {usdt_data['volume_str']} {usdt_icon} {abs(usdt_data['change_percent_24h']):.1f}%\n"

            # Add APY (mock data since we don't have this from API)
            indicators_content += f"💎 **APY:** 3.67%\n\n"

            # Watchlist Table
            table_content = "```\nTOKEN   PRICE      CHG        VOL\n"
            table_content += "─" * 42 + "\n"

            for item in watchlist[:10]:  # Show top 10 tokens
                token = item['token'][:4].ljust(4)
                price = f"${item['price']:.3f}" if item['price'] < 10 else f"${item['price']:.0f}"
                price = price.rjust(10)

                change_icon = "🟢" if item['change_percent_24h'] >= 0 else "🔴"
                change_text = f"{change_icon}{abs(item['change_percent_24h']):.2f}%"
                change_text = change_text.ljust(10)

                volume = item['volume_str'].rjust(8)

                table_content += f"{token} {price} {change_text} {volume}\n"

            table_content += "```\n"

            # Account Status (only if API credentials are available)
            account_content = ""
            if account_info:
                total_balance = client.calculate_total_balance(account_info)
                available_balance = client.calculate_available_balance(account_info)

                # Calculate P&L from futures positions
                total_unrealized_pnl = 0.0
                active_positions_count = 0

                if futures_positions:
                    for pos in futures_positions:
                        unrealized_pnl = float(pos.get('unrealizedPnl', 0))
                        total_unrealized_pnl += unrealized_pnl
                        active_positions_count += 1

                pnl_icon = "🔴" if total_unrealized_pnl < 0 else "🟢"

                account_content = f"""
**💰 Account Status**
Total Balance: ${total_balance:.2f}
Available Balance: ${available_balance:.2f}

**📈 P&L Summary**
Unrealized P&L: {pnl_icon} ${total_unrealized_pnl:.2f}
Open Positions: {active_positions_count}
Pending Orders: {len(open_orders)}
"""

            # Combine all content
            full_content = indicators_content + table_content + account_content
            embed.description = full_content

            # Footer with stats
            total_positions = len(futures_positions) if futures_positions else 0
            embed.set_footer(text=f"📊 {total_positions} tăng | ❌ {len(open_orders)} giảm\nDữ liệu từ MEXC • Tự động cập nhật mỗi 90s • Hôm nay lúc {datetime.now().strftime('%H:%M')}")

            return embed


    
    @tasks.loop(seconds=30)
    async def price_monitor(self):
        try:
            async with MEXCClient() as client:
                price_data = await client.get_price_data()
                if not price_data:
                    return
                
                current_price = price_data['current_price']
                current_volume = price_data['volume']

                # Sử dụng thay đổi trong ngày để monitor
                day_price_change_percent = abs(price_data['day_price_change_percent'])
                price_change_percent = abs((current_price - self.last_price) / self.last_price * 100) if self.last_price > 0 else 0
                volume_change_percent = abs((current_volume - self.last_volume) / self.last_volume * 100) if self.last_volume > 0 else 0
                
                should_alert = False
                alert_type = None
                
                if price_change_percent >= self.price_threshold:
                    should_alert = True
                    alert_type = "price"
                elif volume_change_percent >= self.volume_threshold:
                    should_alert = True
                    alert_type = "volume"
                
                if should_alert:
                    embed = self.create_price_embed(price_data, alert_type=alert_type)
                    await self.paxg_channel.send(embed=embed)
                
                if self.pinned_message:
                    combined_embed = await self.create_combined_dashboard()
                    if combined_embed:
                        await self.pinned_message.edit(embed=combined_embed)

                self.last_price = current_price
                self.last_volume = current_volume
                
        except Exception as e:
            logging.error(f"Error in price_monitor: {e}")
    
    @commands.command(name='paxg')
    async def manual_price_check(self, ctx):
        combined_embed = await self.create_combined_dashboard()
        if combined_embed:
            await ctx.send(embed=combined_embed)
        else:
            await ctx.send("❌ Không thể lấy dữ liệu từ MEXC API")
    
    @commands.command(name='threshold')
    async def set_threshold(self, ctx, price_threshold: float = None, volume_threshold: float = None):
        if ctx.author.id != int(self.config['discord']['admin_id']):
            await ctx.send("❌ Bạn không có quyền sử dụng lệnh này")
            return
        
        if price_threshold is not None:
            self.price_threshold = price_threshold
        if volume_threshold is not None:
            self.volume_threshold = volume_threshold
        
        await ctx.send(f"✅ Đã cập nhật ngưỡng cảnh báo:\n"
                      f"📊 Giá: {self.price_threshold}%\n"
                      f"📦 Volume: {self.volume_threshold}%")

def run_bot():
    bot = PAXGBot()
    bot.run(bot.config['discord']['token'])
