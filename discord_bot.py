import discord
from discord.ext import commands, tasks
import asyncio
import yaml
import logging
from datetime import datetime
from typing import Optional
from mexc_client import MEXCClient

logging.basicConfig(level=logging.INFO)

class PAXGBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = False
        super().__init__(command_prefix='!', intents=intents)
        
        self.config = self.load_config()
        self.paxg_channel = None
        self.pinned_message = None
        self.trading_pinned_message = None
        self.last_price = 0
        self.last_volume = 0
        self.price_threshold = 0.5  # 0.5% thay đổi giá
        self.volume_threshold = 20  # 20% thay đổi volume
        
    def load_config(self):
        with open('config.ymal', 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    
    async def on_ready(self):
        print(f'{self.user} đã kết nối thành công!')
        
        guild = self.get_guild(int(self.config['discord']['guild_id']))
        if guild:
            self.paxg_channel = discord.utils.get(guild.channels, name='paxg')
            if not self.paxg_channel:
                print("Không tìm thấy channel 'paxg'. Vui lòng tạo channel này.")
                return
        
        self.price_monitor.start()
        await self.send_initial_dashboard()
    
    async def send_initial_dashboard(self):
        async with MEXCClient() as client:
            price_data = await client.get_price_data()
            if price_data:
                price_embed = self.create_price_embed(price_data, is_initial=True)
                price_message = await self.paxg_channel.send(embed=price_embed)
                await price_message.pin()
                self.pinned_message = price_message
                self.last_price = price_data['current_price']
                self.last_volume = price_data['volume']

                trading_embed = await self.create_trading_dashboard()
                if trading_embed:
                    trading_message = await self.paxg_channel.send(embed=trading_embed)
                    await trading_message.pin()
                    self.trading_pinned_message = trading_message
    
    def create_price_embed(self, data, is_initial=False, alert_type=None):
        current_price = data['current_price']
        day_open_price = data['day_open_price']
        day_price_change = data['day_price_change']
        day_price_change_percent = data['day_price_change_percent']
        price_change_24h = data['price_change_24h']
        price_change_percent_24h = data['price_change_percent_24h']

        color = discord.Color.green() if day_price_change >= 0 else discord.Color.red()

        if alert_type:
            if alert_type == "price":
                title = "🚨 CẢNH BÁO GIÁ PAXG"
            elif alert_type == "volume":
                title = "🚨 CẢNH BÁO VOLUME PAXG"
            else:
                title = "🚨 CẢNH BÁO PAXG"
        elif is_initial:
            title = "📊 CẬP NHẬT GIÁ PAXG"
        else:
            title = "📊 CẬP NHẬT GIÁ PAXG"

        embed = discord.Embed(title=title, color=color, timestamp=datetime.now())

        # Format giống watchlist - các cột thẳng hàng với monospace
        day_change_icon = "🔴" if day_price_change < 0 else "🟢"
        change_24h_icon = "🔴" if price_change_24h < 0 else "🟢"

        # Tạo nội dung theo format watchlist với cột thẳng hàng sử dụng monospace
        content = f"""```
🧈 Giá hiện tại      ${current_price:>8,.2f}
{day_change_icon} CHG DAY           ${abs(day_price_change):>8,.2f}
📊 Giá mở cửa ngày   ${day_open_price:>8,.2f}
{change_24h_icon} Thay đổi 24h      ${abs(price_change_24h):>8,.2f}
📊 Cao nhất 24h      ${data['high_price']:>8,.2f}
📊 Thấp nhất 24h     ${data['low_price']:>8,.2f}
💎 Volume 24h      {data['volume']:>8,.2f} PAXG
```"""

        embed.description = content
        embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")

        return embed

    async def create_trading_dashboard(self) -> Optional[discord.Embed]:
        async with MEXCClient() as client:
            account_info = await client.get_account_info()
            open_orders = await client.get_open_orders()
            futures_positions = await client.get_futures_positions()

            if not account_info:
                return None

            total_balance = client.calculate_total_balance(account_info)
            available_balance = client.calculate_available_balance(account_info)

            embed = discord.Embed(
                title="📊 Trading Status Dashboard",
                color=discord.Color.blue(),
                timestamp=datetime.now()
            )

            # Account Status - Real data
            account_content = f"""```
💰 Account Status
Total Balance:      ${total_balance:>8,.2f}
Available Balance:  ${available_balance:>8,.2f}
```"""

            # P&L Summary - Mix of real and mock data
            total_unrealized_pnl = 0.0
            active_positions_count = len(futures_positions) if futures_positions else 0

            # If no real futures positions, use mock data for demo
            if active_positions_count == 0:
                total_unrealized_pnl = -80.48
                active_positions_count = 1

            pnl_icon = "🔴" if total_unrealized_pnl < 0 else "🟢"
            pnl_content = f"""```
📈 P&L Summary
Unrealized P&L:     {pnl_icon} ${total_unrealized_pnl:>8,.2f}
Open Positions:     {active_positions_count:>8}
Pending Orders:     {len(open_orders):>8}
```"""

            # Open Positions - Real data if available, otherwise mock
            if futures_positions and len(futures_positions) > 0:
                positions_content = "```\n📊 Open Positions\n"
                for pos in futures_positions[:3]:  # Show max 3 positions
                    symbol = pos.get('symbol', 'N/A')
                    side = 'LONG' if float(pos.get('positionSize', 0)) > 0 else 'SHORT'
                    size = abs(float(pos.get('positionSize', 0)))
                    entry_price = float(pos.get('avgPrice', 0))
                    mark_price = float(pos.get('markPrice', 0))
                    unrealized_pnl = float(pos.get('unrealizedPnl', 0))
                    pnl_icon = "🔴" if unrealized_pnl < 0 else "🟢"

                    positions_content += f"{symbol} {side}           {pnl_icon} ${unrealized_pnl:.2f}\n"
                    positions_content += f"Entry: ${entry_price:.4f} | Mark: ${mark_price:.4f}\n"
                    positions_content += f"Size: {size:.4f}\n\n"
                positions_content += "```"
            else:
                # Mock data for demo
                positions_content = f"""```
📊 Open Positions
BTC SHORT           🔴 $-80.48
Entry: $114,042.0000 | Mark: $122,089.6520
Value: $1,220.90 (0.01)

TP:
$113,656.00 | Value: $1,136.56
$110,600.00 | Value: $1,106.00
SL: None
```"""

            # Pending Orders - Real data
            if open_orders and len(open_orders) > 0:
                orders_content = "```\n📋 Pending Orders\n"
                for order in open_orders[:3]:  # Show max 3 orders
                    symbol = order.get('symbol', 'N/A')
                    side = order.get('side', 'N/A')
                    order_type = order.get('type', 'N/A')
                    price = float(order.get('price', 0))
                    quantity = float(order.get('origQty', 0))
                    orders_content += f"{symbol} {side} {order_type}\n"
                    orders_content += f"Price: ${price:.4f} | Qty: {quantity:.4f}\n\n"
                orders_content += "```"
            else:
                orders_content = "```\n📋 Pending Orders\nNo new position orders.\n```"

            # Combine all content
            full_content = account_content + pnl_content + positions_content + orders_content
            embed.description = full_content
            embed.set_footer(text="MEXC Exchange • Cập nhật mỗi 30 giây")

            return embed
    
    @tasks.loop(seconds=30)
    async def price_monitor(self):
        try:
            async with MEXCClient() as client:
                price_data = await client.get_price_data()
                if not price_data:
                    return
                
                current_price = price_data['current_price']
                current_volume = price_data['volume']

                # Sử dụng thay đổi trong ngày để monitor
                day_price_change_percent = abs(price_data['day_price_change_percent'])
                price_change_percent = abs((current_price - self.last_price) / self.last_price * 100) if self.last_price > 0 else 0
                volume_change_percent = abs((current_volume - self.last_volume) / self.last_volume * 100) if self.last_volume > 0 else 0
                
                should_alert = False
                alert_type = None
                
                if price_change_percent >= self.price_threshold:
                    should_alert = True
                    alert_type = "price"
                elif volume_change_percent >= self.volume_threshold:
                    should_alert = True
                    alert_type = "volume"
                
                if should_alert:
                    embed = self.create_price_embed(price_data, alert_type=alert_type)
                    await self.paxg_channel.send(embed=embed)
                
                if self.pinned_message:
                    embed = self.create_price_embed(price_data)
                    await self.pinned_message.edit(embed=embed)

                if self.trading_pinned_message:
                    trading_embed = await self.create_trading_dashboard()
                    if trading_embed:
                        await self.trading_pinned_message.edit(embed=trading_embed)

                self.last_price = current_price
                self.last_volume = current_volume
                
        except Exception as e:
            logging.error(f"Error in price_monitor: {e}")
    
    @commands.command(name='paxg')
    async def manual_price_check(self, ctx):
        async with MEXCClient() as client:
            price_data = await client.get_price_data()
            if price_data:
                embed = self.create_price_embed(price_data)
                await ctx.send(embed=embed)
            else:
                await ctx.send("❌ Không thể lấy dữ liệu giá PAXG")
    
    @commands.command(name='threshold')
    async def set_threshold(self, ctx, price_threshold: float = None, volume_threshold: float = None):
        if ctx.author.id != int(self.config['discord']['admin_id']):
            await ctx.send("❌ Bạn không có quyền sử dụng lệnh này")
            return
        
        if price_threshold is not None:
            self.price_threshold = price_threshold
        if volume_threshold is not None:
            self.volume_threshold = volume_threshold
        
        await ctx.send(f"✅ Đã cập nhật ngưỡng cảnh báo:\n"
                      f"📊 Giá: {self.price_threshold}%\n"
                      f"📦 Volume: {self.volume_threshold}%")

def run_bot():
    bot = PAXGBot()
    bot.run(bot.config['discord']['token'])
