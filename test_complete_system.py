#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient
from discord_bot import PAXGBot

async def test_complete_system():
    print("🔄 Testing Complete PAXG Bot System...")
    print("=" * 60)
    
    # Test 1: MEXC API Connection
    print("\n1️⃣ Testing MEXC API Connection...")
    async with MEXCClient() as client:
        # Test price data
        price_data = await client.get_price_data()
        if price_data:
            print("✅ Price Data API: Working")
            print(f"   PAXG Price: ${price_data['current_price']:.2f}")
            print(f"   24h Change: ${price_data['price_change_24h']:.2f}")
        else:
            print("❌ Price Data API: Failed")
            return
        
        # Test account info
        account_info = await client.get_account_info()
        if account_info:
            print("✅ Account Info API: Working")
            total_balance = client.calculate_total_balance(account_info)
            available_balance = client.calculate_available_balance(account_info)
            print(f"   Total Balance: ${total_balance:.2f}")
            print(f"   Available Balance: ${available_balance:.2f}")
        else:
            print("❌ Account Info API: Failed")
        
        # Test open orders
        open_orders = await client.get_open_orders()
        if open_orders is not None:
            print(f"✅ Open Orders API: Working ({len(open_orders)} orders)")
        else:
            print("❌ Open Orders API: Failed")
        
        # Test futures positions (may fail due to permissions)
        futures_positions = await client.get_futures_positions()
        if futures_positions is not None:
            print(f"✅ Futures Positions API: Working ({len(futures_positions)} positions)")
        else:
            print("⚠️  Futures Positions API: No access (expected)")
    
    # Test 2: Discord Bot Components
    print("\n2️⃣ Testing Discord Bot Components...")
    bot = PAXGBot()
    
    # Test price embed creation
    if price_data:
        price_embed = bot.create_price_embed(price_data, is_initial=True)
        if price_embed:
            print("✅ Price Embed Creation: Working")
            print(f"   Title: {price_embed.title}")
            print(f"   Color: {price_embed.color}")
        else:
            print("❌ Price Embed Creation: Failed")
    
    # Test trading dashboard creation
    trading_embed = await bot.create_trading_dashboard()
    if trading_embed:
        print("✅ Trading Dashboard Creation: Working")
        print(f"   Title: {trading_embed.title}")
        print(f"   Color: {trading_embed.color}")
    else:
        print("❌ Trading Dashboard Creation: Failed")
    
    # Test 3: Configuration
    print("\n3️⃣ Testing Configuration...")
    try:
        config = bot.config
        if config.get('discord', {}).get('token'):
            print("✅ Discord Token: Configured")
        else:
            print("❌ Discord Token: Missing")
        
        if config.get('mexc', {}).get('api_key'):
            print("✅ MEXC API Key: Configured")
        else:
            print("❌ MEXC API Key: Missing")
        
        if config.get('mexc', {}).get('api_secret'):
            print("✅ MEXC API Secret: Configured")
        else:
            print("❌ MEXC API Secret: Missing")
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
    
    # Test 4: Dashboard Content Preview
    print("\n4️⃣ Dashboard Content Preview...")
    if trading_embed:
        print("📊 Trading Dashboard Preview:")
        print("-" * 40)
        content = trading_embed.description
        lines = content.split('\n')
        for line in lines[:15]:  # Show first 15 lines
            print(line)
        if len(lines) > 15:
            print("... (truncated)")
        print("-" * 40)
    
    print("\n✅ System Test Complete!")
    print("🚀 Bot is ready to run with both price monitoring and trading dashboard!")

if __name__ == "__main__":
    asyncio.run(test_complete_system())
