#!/usr/bin/env python3
import asyncio
import json
from mexc_client import MEXCClient

async def test_futures_api():
    print("🔄 Testing MEXC Futures API...")
    
    async with MEXCClient() as client:
        print("\n💰 Testing Futures Balance...")
        futures_balance = await client.get_futures_balance()
        if futures_balance:
            print("✅ Futures balance retrieved successfully:")
            print(json.dumps(futures_balance, indent=2))
        else:
            print("❌ Failed to get futures balance")
        
        print("\n📊 Testing Futures Positions...")
        positions = await client.get_futures_positions()
        if positions is not None:
            print(f"✅ Futures positions retrieved: {len(positions)} active positions")
            for pos in positions:
                symbol = pos.get('symbol', 'N/A')
                side = 'LONG' if float(pos.get('positionAmt', 0)) > 0 else 'SHORT'
                size = abs(float(pos.get('positionAmt', 0)))
                entry_price = float(pos.get('entryPrice', 0))
                mark_price = float(pos.get('markPrice', 0))
                unrealized_pnl = float(pos.get('unRealizedProfit', 0))
                
                print(f"   {symbol} {side}: Size={size:.4f}")
                print(f"   Entry: ${entry_price:.4f} | Mark: ${mark_price:.4f}")
                print(f"   Unrealized P&L: ${unrealized_pnl:.2f}")
                print()
        else:
            print("❌ Failed to get futures positions")
        
        print("\n📋 Testing Spot Open Orders...")
        open_orders = await client.get_open_orders()
        if open_orders is not None:
            print(f"✅ Spot open orders retrieved: {len(open_orders)} orders")
            for order in open_orders[:3]:
                symbol = order.get('symbol', 'N/A')
                side = order.get('side', 'N/A')
                order_type = order.get('type', 'N/A')
                price = float(order.get('price', 0))
                quantity = float(order.get('origQty', 0))
                print(f"   {symbol} {side} {order_type}: ${price:.4f} x {quantity:.4f}")
        else:
            print("❌ Failed to get open orders")

if __name__ == "__main__":
    asyncio.run(test_futures_api())
