#!/usr/bin/env python3
import asyncio
import discord
from discord_bot import PAXGBot
from mexc_client import MEXCClient

async def test_dashboard_creation():
    print("🔄 Testing Dashboard Creation...")
    
    # Test MEXC client
    async with MEXCClient() as client:
        print("\n📊 Testing Price Data...")
        price_data = await client.get_price_data()
        if price_data:
            print("✅ Price data retrieved successfully")
            print(f"   Current Price: ${price_data['current_price']:.2f}")
        else:
            print("❌ Failed to get price data")
            return
        
        print("\n💰 Testing Account Info...")
        account_info = await client.get_account_info()
        if account_info:
            print("✅ Account info retrieved successfully")
            total_balance = client.calculate_total_balance(account_info)
            available_balance = client.calculate_available_balance(account_info)
            print(f"   Total Balance: ${total_balance:.2f}")
            print(f"   Available Balance: ${available_balance:.2f}")
        else:
            print("❌ Failed to get account info")
        
        print("\n📋 Testing Open Orders...")
        open_orders = await client.get_open_orders()
        print(f"✅ Open orders retrieved: {len(open_orders)} orders")
    
    # Test bot dashboard creation
    print("\n🤖 Testing Bot Dashboard Creation...")
    bot = PAXGBot()
    
    # Test price embed
    if price_data:
        price_embed = bot.create_price_embed(price_data, is_initial=True)
        print("✅ Price embed created successfully")
        print(f"   Title: {price_embed.title}")
        print(f"   Color: {price_embed.color}")
        print(f"   Description length: {len(price_embed.description)} chars")
    
    # Test trading dashboard
    trading_embed = await bot.create_trading_dashboard()
    if trading_embed:
        print("✅ Trading dashboard created successfully")
        print(f"   Title: {trading_embed.title}")
        print(f"   Color: {trading_embed.color}")
        print(f"   Description length: {len(trading_embed.description)} chars")
        
        # Print a preview of the dashboard content
        print("\n📋 Dashboard Preview:")
        print("=" * 50)
        print(trading_embed.description[:500] + "..." if len(trading_embed.description) > 500 else trading_embed.description)
        print("=" * 50)
    else:
        print("❌ Failed to create trading dashboard")

if __name__ == "__main__":
    asyncio.run(test_dashboard_creation())
